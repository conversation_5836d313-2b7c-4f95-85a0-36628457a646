import React from 'react';

const LoadingSkeleton = ({ type = 'card' }) => {
  if (type === 'card') {
    return (
      <div className="bg-gradient-to-br from-gray-50 via-white to-gray-50 border border-gray-200/50 rounded-2xl p-6 shadow-lg animate-pulse">
        {/* Header skeleton */}
        <div className="flex items-center space-x-4 mb-6">
          <div className="w-12 h-12 bg-gray-300 rounded-xl"></div>
          <div className="flex-1">
            <div className="h-4 bg-gray-300 rounded-lg w-24 mb-2"></div>
            <div className="h-3 bg-gray-200 rounded-lg w-32"></div>
          </div>
        </div>
        
        {/* Count skeleton */}
        <div className="mb-6">
          <div className="h-10 bg-gray-300 rounded-lg w-32 mb-2"></div>
        </div>
        
        {/* Trend skeleton */}
        <div className="flex items-center space-x-2 mb-4">
          <div className="h-6 bg-gray-200 rounded-full w-20"></div>
        </div>
        
        {/* Footer skeleton */}
        <div className="pt-4 border-t border-gray-200">
          <div className="h-3 bg-gray-200 rounded-lg w-28"></div>
        </div>
      </div>
    );
  }

  if (type === 'header') {
    return (
      <div className="mb-12 animate-pulse">
        <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-4 lg:space-y-0">
          <div className="space-y-2">
            <div className="flex items-center space-x-3">
              <div className="w-10 h-10 bg-gray-300 rounded-lg"></div>
              <div className="h-8 bg-gray-300 rounded-lg w-48"></div>
            </div>
            <div className="h-4 bg-gray-200 rounded-lg w-64 ml-13"></div>
          </div>
          <div className="flex items-center space-x-4">
            <div className="h-10 bg-gray-200 rounded-full w-48"></div>
            <div className="h-12 bg-gray-300 rounded-full w-24"></div>
          </div>
        </div>
      </div>
    );
  }

  return null;
};

const CardGridSkeleton = () => {
  return (
    <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-4 gap-6 mb-12">
      {[...Array(4)].map((_, index) => (
        <div key={index} className="animate-slide-up" style={{ animationDelay: `${index * 0.1}s` }}>
          <LoadingSkeleton type="card" />
        </div>
      ))}
    </div>
  );
};

export { LoadingSkeleton, CardGridSkeleton };
export default LoadingSkeleton;
