import React from 'react';

const ProgressBar = ({ 
  value, 
  max = 100, 
  color = 'blue',
  size = 'md',
  showLabel = true,
  animated = true 
}) => {
  const percentage = Math.min((value / max) * 100, 100);
  
  const getColorClasses = () => {
    switch (color) {
      case 'green':
        return 'bg-gradient-to-r from-emerald-500 to-green-500';
      case 'red':
        return 'bg-gradient-to-r from-red-500 to-rose-500';
      case 'blue':
        return 'bg-gradient-to-r from-blue-500 to-sky-500';
      case 'purple':
        return 'bg-gradient-to-r from-purple-500 to-violet-500';
      default:
        return 'bg-gradient-to-r from-gray-500 to-slate-500';
    }
  };

  const getSizeClasses = () => {
    switch (size) {
      case 'sm':
        return 'h-2';
      case 'lg':
        return 'h-4';
      default:
        return 'h-3';
    }
  };

  return (
    <div className="w-full">
      {showLabel && (
        <div className="flex justify-between items-center mb-2">
          <span className="text-sm font-medium text-gray-700">T<PERSON><PERSON><PERSON> đ<PERSON></span>
          <span className="text-sm font-bold text-gray-900">{value}%</span>
        </div>
      )}
      <div className={`w-full bg-gray-200 rounded-full overflow-hidden ${getSizeClasses()}`}>
        <div
          className={`${getSizeClasses()} ${getColorClasses()} rounded-full transition-all duration-1000 ease-out ${
            animated ? 'animate-pulse-glow' : ''
          }`}
          style={{ 
            width: `${percentage}%`,
            transition: 'width 1.5s cubic-bezier(0.4, 0, 0.2, 1)'
          }}
        />
      </div>
    </div>
  );
};

export default ProgressBar;
