import React, { useState, useEffect } from 'react';
import { RefreshCw, Activity, Sparkles, Zap, BarChart3 } from 'lucide-react';
import DashboardLayout from '../components/DashboardLayout';
import StatusCard from '../components/StatusCard';
import { LoadingSkeleton, CardGridSkeleton } from '../components/LoadingSkeleton';
import { zaloNotificationService } from '../services/zaloNotificationService';

const DashboardHome = () => {
  const [data, setData] = useState(null);
  const [loading, setLoading] = useState(true);
  const [lastRefresh, setLastRefresh] = useState(new Date());

  // Fetch notification statistics
  const fetchData = async () => {
    try {
      setLoading(true);
      const stats = await zaloNotificationService.getNotificationStats();
      setData(stats);
      setLastRefresh(new Date());
    } catch (error) {
      console.error('Error fetching notification stats:', error);
    } finally {
      setLoading(false);
    }
  };

  // Initial data load
  useEffect(() => {
    fetchData();
  }, []);

  // Auto-refresh every 30 seconds
  useEffect(() => {
    const interval = setInterval(fetchData, 30000);
    return () => clearInterval(interval);
  }, []);

  const handleRefresh = () => {
    fetchData();
  };

  if (loading && !data) {
    return (
      <DashboardLayout>
        <LoadingSkeleton type="header" />
        <CardGridSkeleton />
        <div className="bg-white/80 backdrop-blur-sm rounded-2xl shadow-xl border border-white/40 p-8 animate-pulse">
          <div className="flex items-center space-x-3 mb-6">
            <div className="w-8 h-8 bg-gray-300 rounded-lg"></div>
            <div className="h-6 bg-gray-300 rounded-lg w-32"></div>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            {[...Array(3)].map((_, index) => (
              <div key={index} className="p-6 border-2 border-gray-200 rounded-xl">
                <div className="w-8 h-8 bg-gray-300 rounded-lg mb-3 mx-auto"></div>
                <div className="h-4 bg-gray-300 rounded-lg w-24 mx-auto mb-1"></div>
                <div className="h-3 bg-gray-200 rounded-lg w-16 mx-auto"></div>
              </div>
            ))}
          </div>
        </div>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout>
      {/* Dashboard Header */}
      <div className="mb-12 animate-fade-in">
        <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-4 lg:space-y-0">
          <div className="space-y-2">
            <div className="flex items-center space-x-3">
              <div className="p-2 bg-gradient-to-r from-blue-500 to-purple-500 rounded-lg">
                <Sparkles className="w-6 h-6 text-white" />
              </div>
              <h2 className="text-4xl font-black bg-gradient-to-r from-gray-900 via-blue-800 to-purple-800 bg-clip-text text-transparent">
                Tổng quan
              </h2>
            </div>
            <p className="text-gray-600 text-lg font-medium ml-11">
              Theo dõi hiệu suất thông báo Zalo của bạn trong thời gian thực
            </p>
          </div>
          <div className="flex flex-col sm:flex-row items-start sm:items-center space-y-3 sm:space-y-0 sm:space-x-4">
            <div className="flex items-center space-x-2 px-4 py-2 bg-white/80 backdrop-blur-sm rounded-full border border-white/40 shadow-lg">
              <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
              <Activity size={16} className="text-blue-600" />
              <span className="text-sm text-gray-600 font-medium">
                Cập nhật: {lastRefresh.toLocaleTimeString('vi-VN')}
              </span>
            </div>
            <button
              onClick={handleRefresh}
              disabled={loading}
              className="group flex items-center space-x-2 px-6 py-3 bg-gradient-to-r from-blue-600 to-purple-600 text-white rounded-full hover:from-blue-700 hover:to-purple-700 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-300 shadow-lg hover:shadow-xl transform hover:scale-105"
            >
              <RefreshCw className={`h-4 w-4 ${loading ? 'animate-spin' : 'group-hover:rotate-180'} transition-transform duration-300`} />
              <span className="font-semibold">Làm mới</span>
            </button>
          </div>
        </div>
      </div>

      {/* Status Cards Grid */}
      {data && (
        <div className={`grid grid-cols-1 md:grid-cols-2 xl:grid-cols-4 gap-6 mb-12 transition-opacity duration-300 ${loading ? 'opacity-70' : 'opacity-100'}`}>
          {/* Success Notifications */}
          <div className="animate-slide-up" style={{ animationDelay: '0.1s' }}>
            <StatusCard
              title="Thành công"
              count={data.success.count}
              percentage={data.success.percentage}
              trend={data.success.trend}
              type="success"
              description={data.success.description}
              lastUpdated={data.success.lastUpdated}
            />
          </div>

          {/* Error Notifications */}
          <div className="animate-slide-up" style={{ animationDelay: '0.2s' }}>
            <StatusCard
              title="Lỗi"
              count={data.error.count}
              percentage={data.error.percentage}
              trend={data.error.trend}
              type="error"
              description={data.error.description}
              lastUpdated={data.error.lastUpdated}
            />
          </div>

          {/* Total Notifications */}
          <div className="animate-slide-up" style={{ animationDelay: '0.3s' }}>
            <StatusCard
              title="Tổng số"
              count={data.total.count}
              percentage={data.total.percentage}
              trend={data.total.trend}
              type="info"
              description={data.total.description}
              lastUpdated={data.total.lastUpdated}
            />
          </div>

          {/* Success Rate */}
          <div className="animate-slide-up" style={{ animationDelay: '0.4s' }}>
            <StatusCard
              title="Tỷ lệ thành công"
              count={data.successRate.count}
              percentage={data.successRate.percentage}
              trend={data.successRate.trend}
              type="percentage"
              description={data.successRate.description}
              lastUpdated={data.successRate.lastUpdated}
            />
          </div>
        </div>
      )}

      {/* Quick Actions */}
      <div className="bg-white/80 backdrop-blur-sm rounded-2xl shadow-xl border border-white/40 p-8 animate-slide-up" style={{ animationDelay: '0.5s' }}>
        <div className="flex items-center space-x-3 mb-6">
          <div className="p-2 bg-gradient-to-r from-indigo-500 to-purple-500 rounded-lg">
            <Zap className="w-5 h-5 text-white" />
          </div>
          <h3 className="text-xl font-bold text-gray-900">Thao tác nhanh</h3>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <button className="group flex flex-col items-center justify-center p-6 border-2 border-gray-200 rounded-xl hover:border-blue-300 hover:bg-blue-50/50 transition-all duration-300 transform hover:scale-105">
            <BarChart3 className="w-8 h-8 text-blue-600 mb-3 group-hover:scale-110 transition-transform duration-300" />
            <span className="text-gray-700 font-semibold group-hover:text-blue-700">Xem chi tiết thông báo</span>
            <span className="text-xs text-gray-500 mt-1">Phân tích chi tiết</span>
          </button>
          <button className="group flex flex-col items-center justify-center p-6 border-2 border-gray-200 rounded-xl hover:border-green-300 hover:bg-green-50/50 transition-all duration-300 transform hover:scale-105">
            <Sparkles className="w-8 h-8 text-green-600 mb-3 group-hover:scale-110 transition-transform duration-300" />
            <span className="text-gray-700 font-semibold group-hover:text-green-700">Tạo thông báo mới</span>
            <span className="text-xs text-gray-500 mt-1">Gửi ngay lập tức</span>
          </button>
          <button className="group flex flex-col items-center justify-center p-6 border-2 border-gray-200 rounded-xl hover:border-purple-300 hover:bg-purple-50/50 transition-all duration-300 transform hover:scale-105">
            <Activity className="w-8 h-8 text-purple-600 mb-3 group-hover:scale-110 transition-transform duration-300" />
            <span className="text-gray-700 font-semibold group-hover:text-purple-700">Xem báo cáo</span>
            <span className="text-xs text-gray-500 mt-1">Thống kê tổng quan</span>
          </button>
        </div>
      </div>
    </DashboardLayout>
  );
};

export default DashboardHome;
