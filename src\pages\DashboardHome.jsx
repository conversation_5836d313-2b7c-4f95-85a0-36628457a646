import React, { useState, useEffect } from 'react';
import { RefreshCw, Activity } from 'lucide-react';
import DashboardLayout from '../components/DashboardLayout';
import StatusCard from '../components/StatusCard';
import { zaloNotificationService } from '../services/zaloNotificationService';

const DashboardHome = () => {
  const [data, setData] = useState(null);
  const [loading, setLoading] = useState(true);
  const [lastRefresh, setLastRefresh] = useState(new Date());

  // Fetch notification statistics
  const fetchData = async () => {
    try {
      setLoading(true);
      const stats = await zaloNotificationService.getNotificationStats();
      setData(stats);
      setLastRefresh(new Date());
    } catch (error) {
      console.error('Error fetching notification stats:', error);
    } finally {
      setLoading(false);
    }
  };

  // Initial data load
  useEffect(() => {
    fetchData();
  }, []);

  // Auto-refresh every 30 seconds
  useEffect(() => {
    const interval = setInterval(fetchData, 30000);
    return () => clearInterval(interval);
  }, []);

  const handleRefresh = () => {
    fetchData();
  };

  if (loading && !data) {
    return (
      <DashboardLayout>
        <div className="flex items-center justify-center h-64">
          <div className="flex items-center space-x-2">
            <RefreshCw className="animate-spin h-6 w-6 text-blue-600" />
            <span className="text-lg text-gray-600">Đang tải dữ liệu...</span>
          </div>
        </div>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout>
      {/* Dashboard Header */}
      <div className="mb-8">
        <div className="flex items-center justify-between">
          <div>
            <h2 className="text-3xl font-bold text-gray-900">Tổng quan</h2>
            <p className="text-gray-600 mt-1">
              Theo dõi hiệu suất thông báo Zalo của bạn
            </p>
          </div>
          <div className="flex items-center space-x-4">
            <div className="flex items-center space-x-2 text-sm text-gray-500">
              <Activity size={16} />
              <span>Cập nhật lần cuối: {lastRefresh.toLocaleTimeString('vi-VN')}</span>
            </div>
            <button
              onClick={handleRefresh}
              disabled={loading}
              className="flex items-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
            >
              <RefreshCw className={`h-4 w-4 ${loading ? 'animate-spin' : ''}`} />
              <span>Làm mới</span>
            </button>
          </div>
        </div>
      </div>

      {/* Status Cards Grid */}
      {data && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          {/* Success Notifications */}
          <StatusCard
            title="Thành công"
            count={data.success.count}
            percentage={data.success.percentage}
            trend={data.success.trend}
            type="success"
            description={data.success.description}
            lastUpdated={data.success.lastUpdated}
          />

          {/* Error Notifications */}
          <StatusCard
            title="Lỗi"
            count={data.error.count}
            percentage={data.error.percentage}
            trend={data.error.trend}
            type="error"
            description={data.error.description}
            lastUpdated={data.error.lastUpdated}
          />

          {/* Total Notifications */}
          <StatusCard
            title="Tổng số"
            count={data.total.count}
            percentage={data.total.percentage}
            trend={data.total.trend}
            type="info"
            description={data.total.description}
            lastUpdated={data.total.lastUpdated}
          />

          {/* Success Rate */}
          <StatusCard
            title="Tỷ lệ thành công"
            count={data.successRate.count}
            percentage={data.successRate.percentage}
            trend={data.successRate.trend}
            type="percentage"
            description={data.successRate.description}
            lastUpdated={data.successRate.lastUpdated}
          />
        </div>
      )}

      {/* Quick Actions */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Thao tác nhanh</h3>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <button className="flex items-center justify-center p-4 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors">
            <span className="text-gray-700">Xem chi tiết thông báo</span>
          </button>
          <button className="flex items-center justify-center p-4 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors">
            <span className="text-gray-700">Tạo thông báo mới</span>
          </button>
          <button className="flex items-center justify-center p-4 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors">
            <span className="text-gray-700">Xem báo cáo</span>
          </button>
        </div>
      </div>
    </DashboardLayout>
  );
};

export default DashboardHome;
