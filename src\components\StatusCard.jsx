import React from 'react';
import { CheckCircle, XCircle, TrendingUp, TrendingDown, BarChart3, Percent } from 'lucide-react';

const StatusCard = ({ 
  title, 
  count, 
  percentage, 
  trend, 
  type = 'success',
  description,
  lastUpdated 
}) => {
  // Define colors and icons based on type
  const getCardStyles = () => {
    switch (type) {
      case 'success':
        return {
          bgColor: 'bg-green-50',
          borderColor: 'border-green-200',
          iconColor: 'text-green-600',
          textColor: 'text-green-800',
          countColor: 'text-green-900',
          icon: CheckCircle
        };
      case 'error':
        return {
          bgColor: 'bg-red-50',
          borderColor: 'border-red-200',
          iconColor: 'text-red-600',
          textColor: 'text-red-800',
          countColor: 'text-red-900',
          icon: XCircle
        };
      case 'info':
        return {
          bgColor: 'bg-blue-50',
          borderColor: 'border-blue-200',
          iconColor: 'text-blue-600',
          textColor: 'text-blue-800',
          countColor: 'text-blue-900',
          icon: BarChart3
        };
      case 'percentage':
        return {
          bgColor: 'bg-purple-50',
          borderColor: 'border-purple-200',
          iconColor: 'text-purple-600',
          textColor: 'text-purple-800',
          countColor: 'text-purple-900',
          icon: Percent
        };
      default:
        return {
          bgColor: 'bg-gray-50',
          borderColor: 'border-gray-200',
          iconColor: 'text-gray-600',
          textColor: 'text-gray-800',
          countColor: 'text-gray-900',
          icon: BarChart3
        };
    }
  };

  const styles = getCardStyles();
  const IconComponent = styles.icon;
  const TrendIcon = trend === 'up' ? TrendingUp : TrendingDown;
  const trendColor = trend === 'up' ? 'text-green-600' : 'text-red-600';

  return (
    <div className={`${styles.bgColor} ${styles.borderColor} border rounded-lg p-6 shadow-sm hover:shadow-md transition-shadow duration-200`}>
      {/* Header with icon and title */}
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center space-x-3">
          <div className={`${styles.iconColor} p-2 rounded-full bg-white`}>
            <IconComponent size={24} />
          </div>
          <h3 className={`text-lg font-semibold ${styles.textColor}`}>
            {title}
          </h3>
        </div>
      </div>

      {/* Main count */}
      <div className="mb-4">
        <div className={`text-3xl font-bold ${styles.countColor}`}>
          {count?.toLocaleString('vi-VN') || '0'}
        </div>
        {description && (
          <p className="text-sm text-gray-600 mt-1">{description}</p>
        )}
      </div>

      {/* Percentage and trend */}
      {(percentage !== undefined || trend) && (
        <div className="flex items-center justify-between">
          {percentage !== undefined && (
            <div className="flex items-center space-x-1">
              <TrendIcon size={16} className={trendColor} />
              <span className={`text-sm font-medium ${trendColor}`}>
                {percentage}%
              </span>
              <span className="text-sm text-gray-500">so với hôm qua</span>
            </div>
          )}
        </div>
      )}

      {/* Last updated */}
      {lastUpdated && (
        <div className="mt-4 pt-4 border-t border-gray-200">
          <p className="text-xs text-gray-500">
            Cập nhật lần cuối: {lastUpdated}
          </p>
        </div>
      )}
    </div>
  );
};

export default StatusCard;
