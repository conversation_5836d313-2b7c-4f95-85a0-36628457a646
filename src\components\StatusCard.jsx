import React from 'react';
import { CheckCircle, XCircle, TrendingUp, TrendingDown, BarChart3, Percent } from 'lucide-react';
import ProgressBar from './ProgressBar';

const StatusCard = ({ 
  title, 
  count, 
  percentage, 
  trend, 
  type = 'success',
  description,
  lastUpdated 
}) => {
  // Define colors and icons based on type
  const getCardStyles = () => {
    switch (type) {
      case 'success':
        return {
          bgGradient: 'bg-gradient-to-br from-emerald-50 via-green-50 to-teal-50',
          borderColor: 'border-emerald-200/50',
          iconBg: 'bg-gradient-to-r from-emerald-500 to-green-500',
          iconColor: 'text-white',
          textColor: 'text-emerald-800',
          countColor: 'text-emerald-900',
          accentColor: 'text-emerald-600',
          shadowColor: 'shadow-emerald-100',
          icon: CheckCircle
        };
      case 'error':
        return {
          bgGradient: 'bg-gradient-to-br from-red-50 via-rose-50 to-pink-50',
          borderColor: 'border-red-200/50',
          iconBg: 'bg-gradient-to-r from-red-500 to-rose-500',
          iconColor: 'text-white',
          textColor: 'text-red-800',
          countColor: 'text-red-900',
          accentColor: 'text-red-600',
          shadowColor: 'shadow-red-100',
          icon: XCircle
        };
      case 'info':
        return {
          bgGradient: 'bg-gradient-to-br from-blue-50 via-sky-50 to-cyan-50',
          borderColor: 'border-blue-200/50',
          iconBg: 'bg-gradient-to-r from-blue-500 to-sky-500',
          iconColor: 'text-white',
          textColor: 'text-blue-800',
          countColor: 'text-blue-900',
          accentColor: 'text-blue-600',
          shadowColor: 'shadow-blue-100',
          icon: BarChart3
        };
      case 'percentage':
        return {
          bgGradient: 'bg-gradient-to-br from-purple-50 via-violet-50 to-indigo-50',
          borderColor: 'border-purple-200/50',
          iconBg: 'bg-gradient-to-r from-purple-500 to-violet-500',
          iconColor: 'text-white',
          textColor: 'text-purple-800',
          countColor: 'text-purple-900',
          accentColor: 'text-purple-600',
          shadowColor: 'shadow-purple-100',
          icon: Percent
        };
      default:
        return {
          bgGradient: 'bg-gradient-to-br from-gray-50 via-slate-50 to-zinc-50',
          borderColor: 'border-gray-200/50',
          iconBg: 'bg-gradient-to-r from-gray-500 to-slate-500',
          iconColor: 'text-white',
          textColor: 'text-gray-800',
          countColor: 'text-gray-900',
          accentColor: 'text-gray-600',
          shadowColor: 'shadow-gray-100',
          icon: BarChart3
        };
    }
  };

  const styles = getCardStyles();
  const IconComponent = styles.icon;
  const TrendIcon = trend === 'up' ? TrendingUp : TrendingDown;
  const trendColor = trend === 'up' ? 'text-green-600' : 'text-red-600';

  return (
    <div className={`${styles.bgGradient} ${styles.borderColor} border backdrop-blur-sm rounded-2xl p-6 ${styles.shadowColor} shadow-lg hover:shadow-xl hover:scale-[1.02] transition-all duration-300 group relative overflow-hidden`}>
      {/* Decorative background pattern */}
      <div className="absolute inset-0 opacity-5">
        <div className="absolute top-0 right-0 w-32 h-32 rounded-full bg-white transform translate-x-16 -translate-y-16"></div>
        <div className="absolute bottom-0 left-0 w-24 h-24 rounded-full bg-white transform -translate-x-12 translate-y-12"></div>
      </div>

      {/* Header with icon and title */}
      <div className="relative flex items-center justify-between mb-6">
        <div className="flex items-center space-x-4">
          <div className={`${styles.iconBg} p-3 rounded-xl shadow-lg group-hover:scale-110 transition-transform duration-300`}>
            <IconComponent size={24} className={styles.iconColor} />
          </div>
          <div>
            <h3 className={`text-lg font-bold ${styles.textColor} mb-1`}>
              {title}
            </h3>
            {description && (
              <p className="text-sm text-gray-600 font-medium">{description}</p>
            )}
          </div>
        </div>
      </div>

      {/* Main count */}
      <div className="relative mb-6">
        <div className={`text-4xl font-black ${styles.countColor} mb-2 tracking-tight number-display`}>
          {count?.toLocaleString('vi-VN') || '0'}
          {type === 'percentage' && <span className="text-2xl ml-1">%</span>}
        </div>
        {type === 'percentage' && (
          <div className="mt-4">
            <ProgressBar
              value={count}
              color={count >= 90 ? 'green' : count >= 70 ? 'blue' : 'red'}
              size="md"
              showLabel={false}
              animated={true}
            />
          </div>
        )}
      </div>

      {/* Percentage and trend */}
      {(percentage !== undefined || trend) && (
        <div className="relative flex items-center justify-between">
          {percentage !== undefined && (
            <div className="flex items-center space-x-2 px-3 py-2 bg-white/60 rounded-full backdrop-blur-sm">
              <div className={`p-1 rounded-full ${trend === 'up' ? 'bg-emerald-100' : 'bg-red-100'}`}>
                <TrendIcon size={14} className={trendColor} />
              </div>
              <span className={`text-sm font-bold ${trendColor}`}>
                {percentage}%
              </span>
              <span className="text-xs text-gray-600 font-medium">hôm qua</span>
            </div>
          )}
        </div>
      )}

      {/* Last updated */}
      {lastUpdated && (
        <div className="relative mt-6 pt-4 border-t border-white/30">
          <p className="text-xs text-gray-500 font-medium flex items-center space-x-1">
            <span className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></span>
            <span>Cập nhật: {lastUpdated}</span>
          </p>
        </div>
      )}
    </div>
  );
};

export default StatusCard;
