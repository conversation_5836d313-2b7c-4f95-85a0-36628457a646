# Zalo Notifications Dashboard v2

A modern React dashboard for monitoring Zalo notification performance with real-time statistics and visual indicators.

## Features

### 🎯 Dashboard Overview
- **Card-based Layout**: Clean, responsive design with status cards
- **Real-time Updates**: Auto-refresh every 30 seconds
- **Vietnamese Localization**: Full Vietnamese language support

### 📊 Status Cards
- **Thành công (Success)**: Green-themed cards showing successful notifications
- **Lỗi (Error)**: Red-themed cards displaying failed notifications
- **Tổng số (Total)**: Blue-themed cards with total notification counts
- **Tỷ lệ thành công (Success Rate)**: Purple-themed cards showing success percentage

### 🎨 Visual Design
- **Color-coded Status**: Different colors for success (green) and error (red) states
- **Lucide React Icons**: Modern icons for better visual distinction
- **Trend Indicators**: Up/down arrows showing performance trends
- **Responsive Grid**: Adapts to different screen sizes (1-4 columns)

### 📈 Data Features
- **Mock Data Service**: Simulated Zalo notification data
- **Percentage Changes**: Shows day-over-day performance changes
- **Last Updated Timestamps**: Real-time update indicators
- **Vietnamese Number Formatting**: Proper locale formatting

## Tech Stack

- **React 19.1.1**: Latest React with modern features
- **Vite 7.1.7**: Fast build tool and dev server
- **Tailwind CSS 4.1.13**: Utility-first CSS framework
- **Lucide React 0.544.0**: Beautiful icon library
- **React Router 7.9.1**: Client-side routing (ready for future pages)

## Getting Started

### Prerequisites
- Node.js (>= 18.18.0)
- npm or yarn

### Installation
```bash
# Install dependencies
npm install

# Start development server
npm run dev

# Build for production
npm run build

# Preview production build
npm run preview
```

### Development
The app will be available at `http://localhost:5173` (or next available port).

## Project Structure

```
src/
├── components/
│   ├── DashboardLayout.jsx    # Main layout wrapper
│   └── StatusCard.jsx         # Reusable status card component
├── pages/
│   └── DashboardHome.jsx      # Main dashboard page
├── services/
│   └── zaloNotificationService.js  # Mock data service
└── App.jsx                    # Root component
```

## Future Enhancements

- **Real API Integration**: Replace mock service with actual Zalo API
- **Historical Charts**: Add trend graphs and historical data views
- **WebSocket Support**: Real-time updates without polling
- **User Authentication**: Secure access to dashboard
- **Export Features**: PDF/Excel report generation
- **Mobile App**: React Native version for mobile access

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## License

This project is private and proprietary to Hyundai SVK.
