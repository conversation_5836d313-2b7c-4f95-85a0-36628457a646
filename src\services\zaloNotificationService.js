// Mock data service for Zalo Notifications
// This will be replaced with real API calls in the future

const generateRandomData = () => {
  const now = new Date();
  const successCount = Math.floor(Math.random() * 10000) + 5000;
  const errorCount = Math.floor(Math.random() * 500) + 50;
  const totalCount = successCount + errorCount;
  
  return {
    success: {
      count: successCount,
      percentage: Math.floor(Math.random() * 20) + 5, // 5-25% increase
      trend: 'up',
      description: 'Thông báo gửi thành công',
      lastUpdated: now.toLocaleTimeString('vi-VN', {
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
      })
    },
    error: {
      count: errorCount,
      percentage: Math.floor(Math.random() * 15) + 2, // 2-17% change
      trend: Math.random() > 0.5 ? 'down' : 'up',
      description: 'Thông báo gửi thất bại',
      lastUpdated: now.toLocaleTimeString('vi-VN', {
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
      })
    },
    total: {
      count: totalCount,
      percentage: Math.floor(Math.random() * 15) + 8, // 8-23% increase
      trend: 'up',
      description: 'Tổng số thông báo đã gửi',
      lastUpdated: now.toLocaleTimeString('vi-VN', {
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
      })
    },
    successRate: {
      count: Math.round((successCount / totalCount) * 100),
      percentage: Math.floor(Math.random() * 5) + 1, // 1-6% change
      trend: 'up',
      description: 'Tỷ lệ thành công (%)',
      lastUpdated: now.toLocaleTimeString('vi-VN', {
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
      })
    }
  };
};

// Simulate API delay
const delay = (ms) => new Promise(resolve => setTimeout(resolve, ms));

export const zaloNotificationService = {
  // Get current notification statistics
  async getNotificationStats() {
    await delay(500); // Simulate API call delay
    return generateRandomData();
  },

  // Get historical data (for future use)
  async getHistoricalData(days = 7) {
    await delay(800);
    const data = [];
    for (let i = days - 1; i >= 0; i--) {
      const date = new Date();
      date.setDate(date.getDate() - i);
      data.push({
        date: date.toISOString().split('T')[0],
        ...generateRandomData()
      });
    }
    return data;
  },

  // Get real-time updates (for future WebSocket integration)
  async getRealtimeUpdates() {
    await delay(200);
    return generateRandomData();
  }
};

export default zaloNotificationService;
